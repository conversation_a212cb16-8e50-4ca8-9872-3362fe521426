#!/usr/bin/env python3
"""
Gradio Web Interface for Qwen3-8B Python Coder
Provides an easy-to-use web UI for testing the fine-tuned model
"""

import gradio as gr
import torch
import logging
import time
import json
from typing import List, Tuple
import os

# Import our inference class
from inference_script import Qwen3PythonCoder

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebInterface:
    """Web interface wrapper for the Qwen3 Python Coder"""
    
    def __init__(self, model_path: str = "./qwen3-python-coder"):
        """Initialize the web interface"""
        self.model_path = model_path
        self.coder = None
        self.is_loaded = False
        
    def load_model(self) -> str:
        """Load the model and return status message"""
        try:
            logger.info("Loading Qwen3 Python Coder model...")
            self.coder = Qwen3PythonCoder(model_path=self.model_path)
            self.is_loaded = True
            return "✅ Model loaded successfully! Ready to generate Python code."
        except Exception as e:
            error_msg = f"❌ Failed to load model: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def generate_code(
        self,
        instruction: str,
        input_text: str = "",
        max_tokens: int = 512,
        temperature: float = 0.7,
        top_p: float = 0.9,
        num_outputs: int = 1,
    ) -> Tuple[str, str]:
        """
        Generate code and return result with metadata
        
        Returns:
            Tuple of (generated_code, metadata)
        """
        if not self.is_loaded:
            return "❌ Model not loaded. Please load the model first.", ""
        
        if not instruction.strip():
            return "❌ Please provide an instruction.", ""
        
        try:
            start_time = time.time()
            
            # Generate code
            responses = self.coder.generate_code(
                instruction=instruction,
                input_text=input_text,
                max_new_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                num_return_sequences=num_outputs,
            )
            
            generation_time = time.time() - start_time
            
            # Format output
            if num_outputs == 1:
                generated_code = responses[0]
            else:
                generated_code = "\n\n" + "="*50 + "\n\n".join(
                    [f"Output {i+1}:\n{resp}" for i, resp in enumerate(responses)]
                )
            
            # Create metadata
            metadata = f"""
📊 Generation Info:
• Time: {generation_time:.2f}s
• Tokens: ~{max_tokens} max
• Temperature: {temperature}
• Top-p: {top_p}
• Outputs: {num_outputs}
• Model: Qwen3-8B + LoRA
            """.strip()
            
            return generated_code, metadata
            
        except Exception as e:
            error_msg = f"❌ Generation failed: {str(e)}"
            logger.error(error_msg)
            return error_msg, ""

# Global interface instance
web_interface = WebInterface()

def create_interface():
    """Create and configure the Gradio interface"""
    
    # Custom CSS for better styling
    css = """
    .gradio-container {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .code-output {
        font-family: 'Courier New', monospace;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 10px;
    }
    """
    
    with gr.Blocks(css=css, title="Qwen3 Python Coder", theme=gr.themes.Soft()) as interface:
        
        # Header
        gr.Markdown("""
        # 🐍 Qwen3-8B Python Coder
        ### Fine-tuned AI Assistant for Python Code Generation
        
        This model has been fine-tuned on 18k Python code instructions to help you generate high-quality Python code.
        """)
        
        # Model loading section
        with gr.Row():
            with gr.Column(scale=3):
                load_btn = gr.Button("🚀 Load Model", variant="primary", size="lg")
            with gr.Column(scale=7):
                load_status = gr.Textbox(
                    label="Model Status",
                    value="🔄 Click 'Load Model' to initialize the AI coder",
                    interactive=False
                )
        
        gr.Markdown("---")
        
        # Main interface
        with gr.Row():
            # Input column
            with gr.Column(scale=1):
                gr.Markdown("### 📝 Input")
                
                instruction = gr.Textbox(
                    label="Instruction",
                    placeholder="e.g., Write a function to calculate fibonacci numbers",
                    lines=3,
                    max_lines=5
                )
                
                input_text = gr.Textbox(
                    label="Input (Optional)",
                    placeholder="e.g., [1, 2, 3, 4, 5] or additional context",
                    lines=2,
                    max_lines=3
                )
                
                # Generation parameters
                with gr.Accordion("⚙️ Generation Settings", open=False):
                    max_tokens = gr.Slider(
                        minimum=50,
                        maximum=1024,
                        value=512,
                        step=50,
                        label="Max Tokens"
                    )
                    
                    temperature = gr.Slider(
                        minimum=0.1,
                        maximum=2.0,
                        value=0.7,
                        step=0.1,
                        label="Temperature (creativity)"
                    )
                    
                    top_p = gr.Slider(
                        minimum=0.1,
                        maximum=1.0,
                        value=0.9,
                        step=0.05,
                        label="Top-p (nucleus sampling)"
                    )
                    
                    num_outputs = gr.Slider(
                        minimum=1,
                        maximum=3,
                        value=1,
                        step=1,
                        label="Number of outputs"
                    )
                
                generate_btn = gr.Button("🎯 Generate Code", variant="primary", size="lg")
            
            # Output column
            with gr.Column(scale=2):
                gr.Markdown("### 🤖 Generated Code")
                
                generated_code = gr.Textbox(
                    label="Python Code",
                    lines=20,
                    max_lines=30,
                    show_copy_button=True,
                    elem_classes=["code-output"]
                )
                
                metadata = gr.Textbox(
                    label="Generation Info",
                    lines=6,
                    interactive=False
                )
        
        # Example prompts
        gr.Markdown("### 💡 Example Prompts")
        
        examples = [
            ["Write a function to calculate the factorial of a number", ""],
            ["Create a class for a simple calculator", ""],
            ["Implement a binary search algorithm", "[1, 3, 5, 7, 9, 11, 13, 15]"],
            ["Write a decorator to measure function execution time", ""],
            ["Create a function to validate email addresses using regex", ""],
            ["Implement a simple web scraper using requests and BeautifulSoup", "https://example.com"],
            ["Write a function to merge two sorted lists", "list1 = [1, 3, 5], list2 = [2, 4, 6]"],
            ["Create a context manager for file operations", ""],
        ]
        
        gr.Examples(
            examples=examples,
            inputs=[instruction, input_text],
            label="Click any example to try it:"
        )
        
        # Event handlers
        load_btn.click(
            fn=web_interface.load_model,
            outputs=load_status
        )
        
        generate_btn.click(
            fn=web_interface.generate_code,
            inputs=[instruction, input_text, max_tokens, temperature, top_p, num_outputs],
            outputs=[generated_code, metadata]
        )
        
        # Footer
        gr.Markdown("""
        ---
        ### 📚 Tips for Better Results:
        - Be specific in your instructions
        - Provide examples or context when helpful
        - Adjust temperature: lower (0.3-0.5) for more focused code, higher (0.7-1.0) for more creative solutions
        - Use the input field for additional context, examples, or constraints
        
        **Model:** Qwen3-8B fine-tuned with LoRA on Python code instructions
        """)
    
    return interface

def main():
    """Launch the web interface"""
    interface = create_interface()
    
    # Launch with configuration
    interface.launch(
        server_name="0.0.0.0",  # Allow external access
        server_port=7860,
        share=False,  # Set to True if you want a public link
        show_error=True,
        show_tips=True,
        enable_queue=True,
        max_threads=4,
    )

if __name__ == "__main__":
    main()
