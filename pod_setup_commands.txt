# Commands to run on the RunPod instance to set up and start training

# 1. First, receive the training files
runpodctl receive 6781-loyal-fashion-lima-1

# 2. Extract the training package
tar -xzf qwen3_training_package_4090.tar.gz

# 3. Make scripts executable
chmod +x setup_environment.sh

# 4. Run the setup script
./setup_environment.sh

# 5. Test the setup
python test_setup.py

# 6. Start training (this will take several hours)
./run_training.sh

# Alternative: Run training in background with nohup
# nohup ./run_training.sh > training_output.log 2>&1 &

# Monitor training progress:
# tail -f logs/*/training.log

# Monitor system resources:
# python monitor_training.py

# Check GPU usage:
# nvidia-smi

# If you need to stop training:
# pkill -f qwen3_training.py
