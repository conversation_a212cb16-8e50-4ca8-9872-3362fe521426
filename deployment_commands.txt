# Commands to deploy the inference setup on a new RunPod

# 1. Configure RunPod CLI on the new pod
runpodctl config --apiKey=rpa_LQNK7VYR4VETRKRICEZUPT0A50BB84L2L705I506sg6ffj

# 2. Receive the inference package (you'll get a new transfer code)
runpodctl receive [TRANSFER_CODE]

# 3. Extract the inference package
tar -xzf qwen3_inference_package.tar.gz

# 4. Make setup script executable
chmod +x setup_inference.sh

# 5. Run the setup
./setup_inference.sh

# 6. Copy your trained model from the training pod
# You'll need to download qwen3-python-coder folder from training pod
# and upload it to the inference pod

# 7. Verify the model
python verify_model.py

# 8. Run quick test
python quick_test.py

# 9. Start the web interface
./run_web.sh

# Alternative: Start CLI interface
# ./run_cli.sh

# Alternative: Run comprehensive tests
# python test_suite.py
